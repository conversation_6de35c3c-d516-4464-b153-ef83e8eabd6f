import {Parameter, RiskRating} from './template';
export interface RiskListResponse {
  message: string;
  result: {
    data: RiskItem[];
    pagination: Pagination;
  };
}

export interface RiskItem {
  id: number;
  template_id: number | null;
  task_requiring_ra: string;
  assessor: number;
  vessel_ownership_id: number | null;
  vessel_id: number | null;
  vessel_code: string | null;
  vessel_name: string | null;
  vessel_tech_group: string | null;
  vessel_category: string | null;
  office_id: number | null;
  office_name: string | null;
  date_risk_assessment: string;
  task_duration: string;
  ra_level: number | null;
  task_alternative_consideration: string;
  task_rejection_reason: string;
  worst_case_scenario: string;
  recovery_measures: string;
  status: number;
  publish_on: string;
  approval_date: string | null;
  draft_step: number | null;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface Pagination {
  totalItems: number;
  totalPages: number;
  page: number;
  pageSize: number;
}

// Risk Form interfaces
export interface RiskTeamMember {
  seafarer_id: number;
  seafarer_person_id: number;
  seafarer_hkid: number;
  seafarer_rank_id: number;
  seafarer_name: string;
  seafarer_rank: string;
  seafarer_rank_sort_order: number;
}

export interface RiskCategory {
  is_other: boolean;
  category_id: number[];
  value: string;
}

export interface RiskHazard {
  is_other: boolean;
  hazard_id: number[];
  value: string;
}

export interface RiskJobInitialRiskRating {
  parameter_type_id: number;
  rating: string;
}

export interface RiskJobResidualRiskRating {
  parameter_type_id: number;
  rating: string;
  reason: string;
}

// Form-specific interface for risk jobs (similar to TemplateFormJob)
export interface RiskFormJob {
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_additional_mitigation: string;
  job_close_out_date: string;
  job_existing_control: string;
  job_close_out_responsibility_id: string;
  risk_job_initial_risk_rating: RiskJobInitialRiskRating[];
  risk_job_residual_risk_rating: RiskJobResidualRiskRating[];
}

// Database entity interface for risk jobs
export interface RiskJob {
  id: number;
  risk_id: number;
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_additional_mitigation: string;
  job_close_out_date: string;
  job_existing_control: string;
  job_close_out_responsibility_id: string;
  risk_job_initial_risk_rating: RiskRating[];
  risk_job_residual_risk_rating: RiskRating[];
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface RiskTaskReliabilityAssessment {
  task_reliability_assessment_id: number;
  task_reliability_assessment_answer: string;
  condition: string;
}

export interface RiskForm {
  task_requiring_ra: string;
  assessor: number;
  vessel_ownership_id: number;
  vessel_id?: number;
  date_risk_assessment: string;
  task_duration: string;
  task_alternative_consideration: string;
  task_rejection_reason: string;
  worst_case_scenario: string;
  recovery_measures: string;
  status: string;
  approval_required: number[];
  risk_team_member: RiskTeamMember[];
  risk_category: RiskCategory;
  risk_hazard: RiskHazard;
  parameters: Parameter[];
  risk_job: RiskFormJob[];
  risk_task_reliability_assessment: RiskTaskReliabilityAssessment[];
}
