import React, {
  useState,
  forwardRef,
  useImperative<PERSON><PERSON><PERSON>,
  useEffect,
} from 'react';
import {Col, Form, Row} from 'react-bootstrap';
import {InputComponent} from '../../components/InputComponent';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types';
import DropdownTypeahead from '../../components/DropdownTypeahead';
import CustomDatePicker from '../../components/CustomDatePicker';
import {
  getApprovalsRequiredList,
  getOfficesList,
  getVesselsList,
} from '../../services/services';

const errorMsg = 'This is a mandatory field. Please fill to process.';

// Field configuration for validation
const REQUIRED_FIELDS = [
  'task_requiring_ra',
  'task_duration',
  'task_alternative_consideration',
  'task_rejection_reason',
] as const;

const RISK_REQUIRED_FIELDS = [
  'task_requiring_ra',
  'task_duration',
  'task_alternative_consideration',
  'task_rejection_reason',
  'assessor',
  'vessel_ownership_id',
  'date_risk_assessment',
  'approval_required',
] as const;

export const assessorOptions = [
  {value: 1, label: 'Office'},
  {value: 2, label: 'Vessel'},
];

// Utility functions to reduce cognitive complexity
const getFieldValue = (
  form: TemplateForm | RiskForm,
  fieldName: string,
): string => {
  const value = form[fieldName as keyof (TemplateForm | RiskForm)];
  return fieldName === 'task_duration'
    ? value?.toString() || ''
    : (value as string) || '';
};

const isFieldEmpty = (
  form: TemplateForm | RiskForm,
  fieldName: string,
): boolean => {
  // Special handling for numeric fields in RiskForm
  if (fieldName === 'assessor' || fieldName === 'vessel_ownership_id') {
    const riskForm = form as RiskForm;
    const value = riskForm[fieldName as keyof RiskForm];
    return !value || (typeof value === 'number' && value === 0);
  }
  // Special handling for approval_required array field
  if (fieldName === 'approval_required') {
    const riskForm = form as RiskForm;
    const value = riskForm.approval_required;
    return !value || (Array.isArray(value) && value.length === 0);
  }
  const value = getFieldValue(form, fieldName);
  return !value || value?.trim() === '';
};

const validateAllFields = (
  form: TemplateForm | RiskForm,
  type: string,
): {[key: string]: string} => {
  const errors: {[key: string]: string} = {};
  const fieldsToValidate =
    type === 'risk' ? RISK_REQUIRED_FIELDS : REQUIRED_FIELDS;

  fieldsToValidate.forEach(field => {
    if (isFieldEmpty(form, field)) {
      errors[field] = errorMsg;
    }
  });
  return errors;
};

const validateSingleField = (
  form: TemplateForm | RiskForm,
  fieldName: string,
): string => {
  return isFieldEmpty(form, fieldName) ? errorMsg : '';
};

const BasicDetails = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      type?: string;
    },
    ref,
  ) => {
    const [validationErrors, setValidationErrors] = useState<{
      [key: string]: string;
    }>({});
    const [touched, setTouched] = useState<{[key: string]: boolean}>({});
    const [vesselOptions, setVesselOptions] = useState<
      {value: number; label: string; vesselId: number}[]
    >([]);
    const [officeOptions, setOfficeOptions] = useState<
      {value: number; label: string}[]
    >([]);
    const [approvalOptions, setApprovalOptions] = useState<
      {value: number; label: string}[]
    >([]);

    // Simplified validate function
    const validate = (customForm?: TemplateForm | RiskForm) => {
      const f = customForm || form;
      const errors = validateAllFields(f, type);
      const isValid = Object.keys(errors).length === 0;

      if (onValidate) onValidate(isValid);
      setValidationErrors(prev => ({...prev, ...errors}));
      return isValid;
    };

    // Simplified handleChange function
    const handleChange = (e: any) => {
      const {name, value} = e.target;
      const updatedForm = {...form, [name]: value};
      setForm(updatedForm);
      setTouched(prev => ({...prev, [name]: true}));

      const fieldError = validateSingleField(updatedForm, name);
      setValidationErrors(prev => ({...prev, [name]: fieldError}));
      validate(updatedForm);
    };

    // Handle dropdown changes for risk form
    const handleDropdownChange = (name: string, value: any) => {
      const updatedForm = {...form, [name]: value};
      setForm(updatedForm);
      setTouched(prev => ({...prev, [name]: true}));

      const fieldError = validateSingleField(updatedForm, name);
      setValidationErrors(prev => ({...prev, [name]: fieldError}));
      validate(updatedForm);
    };

    // Handle date changes for risk form
    const handleDateChange = (name: string, date?: Date) => {
      setTouched(prev => ({...prev, [name]: true}));

      const updatedForm = {
        ...form,
        [name]: date ? date.toISOString().split('T')[0] : '',
      };
      setForm(updatedForm);

      const fieldError = validateSingleField(updatedForm, name);
      setValidationErrors(prev => ({...prev, [name]: fieldError}));
      validate(updatedForm);
    };

    // Handle assessor dropdown change
    const handleAssessorChange = (selected: any) => {
      const value = Array.isArray(selected)
        ? selected[0]?.value
        : selected?.value;
      setTouched(prev => ({...prev, assessor: true}));
      handleDropdownChange('assessor', value || 0);
    };

    // Handle vessel/office dropdown change
    const handleVesselOfficeChange = (selected: any) => {
      const value = Array.isArray(selected)
        ? selected[0]?.value
        : selected?.value;
      const vesselId = Array.isArray(selected)
        ? selected[0]?.vesselId
        : selected?.vesselId;

      setTouched(prev => ({...prev, vessel_ownership_id: true}));

      // Update both vessel_ownership_id and vessel_id for risk forms
      const updatedForm = {
        ...form,
        vessel_ownership_id: value || 0,
        ...(vesselId && {vessel_id: vesselId}),
      };
      setForm(updatedForm);

      const fieldError = !value ? errorMsg : '';
      setValidationErrors(prev => ({...prev, vessel_ownership_id: fieldError}));
      validate(updatedForm);
    };

    // Handle approval required dropdown change
    const handleApprovalChange = (selected: any) => {
      const values = Array.isArray(selected)
        ? selected.map(item => item.value)
        : selected
        ? [selected.value]
        : [];

      const updatedForm = {...form, approval_required: values};
      setForm(updatedForm);
      setTouched(prev => ({...prev, approval_required: true}));

      const fieldError = validateSingleField(updatedForm, 'approval_required');
      setValidationErrors(prev => ({
        ...prev,
        approval_required: fieldError,
      }));
      validate(updatedForm);
    };

    const handleBlur = (e: any) => {
      const {name} = e.target;
      setTouched(prev => ({...prev, [name]: true}));

      const fieldError = validateSingleField(form, name);
      setValidationErrors(prev => ({...prev, [name]: fieldError}));
      validate();
    };

    useImperativeHandle(ref, () => ({
      validate,
    }));

    // Load vessel and office options for risk forms
    useEffect(() => {
      if (type === 'risk') {
        const loadOptions = async () => {
          try {
            const [vesselResponse, officeResponse, approvalList] =
              await Promise.all([
                getVesselsList(),
                getOfficesList(),
                getApprovalsRequiredList(),
              ]);
            setVesselOptions(
              vesselResponse.map(item => ({
                value: item.id,
                label: item.name,
                vesselId: item.vessel.id,
              })),
            );

            setOfficeOptions(
              officeResponse.map(item => ({
                value: item.id,
                label: item.value,
              })),
            );

            setApprovalOptions(
              approvalList.map(item => ({
                value: item.id,
                label: item.name,
              })),
            );
          } catch (error) {
            console.error('Error loading vessel/office options:', error);
          }
        };

        loadOptions();
      }
    }, [type]);

    return (
      <Col className="ra-negate-padding">
        <div
          className="mb-3 fs-28 primary-color"
          style={{color: '#1F4A70', fontSize: '20px', fontWeight: 600}}
        >
          Enter Basic RA Details
        </div>
        <Form className="ra-negate-padding">
          <Col md={9} className="ra-negate-padding">
            <InputComponent
              label="Task Requiring R.A."
              name="task_requiring_ra"
              value={form.task_requiring_ra}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Enter the task requiring risk assessment"
              type="text"
              maxLength={255}
              formControlId="task_requiring_ra"
              form={form}
              error={
                touched.task_requiring_ra
                  ? validationErrors.task_requiring_ra
                  : ''
              }
            />
          </Col>
          {type === 'risk' && (
            <>
              <Row className="mb-3">
                <Col md={4}>
                  <DropdownTypeahead
                    label="Assessor"
                    options={assessorOptions}
                    selected={
                      assessorOptions.find(
                        opt => opt.value === (form as RiskForm).assessor,
                      ) || null
                    }
                    onChange={handleAssessorChange}
                    onInputChange={() =>
                      setTouched(prev => ({...prev, assessor: true}))
                    }
                    onBlur={() =>
                      setTouched(prev => ({...prev, assessor: true}))
                    }
                    isInvalid={touched.assessor && !!validationErrors.assessor}
                    errorMessage={validationErrors.assessor || errorMsg}
                  />
                </Col>

                <Col md={5}>
                  <DropdownTypeahead
                    label="Vessel/Office"
                    options={
                      (form as RiskForm).assessor === 2
                        ? vesselOptions
                        : officeOptions
                    }
                    selected={
                      (form as RiskForm).assessor === 2
                        ? vesselOptions.find(
                            opt =>
                              opt.value ===
                              (form as RiskForm).vessel_ownership_id,
                          ) || null
                        : officeOptions.find(
                            opt =>
                              opt.value ===
                              (form as RiskForm).vessel_ownership_id,
                          ) || null
                    }
                    onChange={handleVesselOfficeChange}
                    onInputChange={() =>
                      setTouched(prev => ({...prev, vessel_ownership_id: true}))
                    }
                    onBlur={() =>
                      setTouched(prev => ({...prev, vessel_ownership_id: true}))
                    }
                    isInvalid={
                      touched.vessel_ownership_id &&
                      !!validationErrors.vessel_ownership_id
                    }
                    errorMessage={
                      validationErrors.vessel_ownership_id || errorMsg
                    }
                  />
                </Col>
              </Row>
              <Row className="mb-3">
                <Col md={4}>
                  <CustomDatePicker
                    label="Date of Risk Assessment"
                    value={
                      (form as RiskForm).date_risk_assessment
                        ? new Date((form as RiskForm).date_risk_assessment)
                        : undefined
                    }
                    onChange={date =>
                      handleDateChange('date_risk_assessment', date)
                    }
                    placeholder="Select Date"
                    controlId="date_risk_assessment"
                    isRequired={true}
                    errorMsg={
                      touched.date_risk_assessment
                        ? validationErrors.date_risk_assessment
                        : ''
                    }
                    minDate={undefined}
                  />
                </Col>
                <Col md={5}>
                  <DropdownTypeahead
                    label="Approvals Required (if necessary)"
                    options={approvalOptions}
                    selected={approvalOptions.filter(opt =>
                      (form as RiskForm).approval_required?.includes(opt.value),
                    )}
                    onChange={handleApprovalChange}
                    multiple
                    onInputChange={() =>
                      setTouched(prev => ({...prev, approval_required: true}))
                    }
                    onBlur={() =>
                      setTouched(prev => ({...prev, approval_required: true}))
                    }
                    isInvalid={
                      touched.approval_required &&
                      !!validationErrors.approval_required
                    }
                    errorMessage={
                      validationErrors.approval_required || errorMsg
                    }
                  />
                </Col>
              </Row>
            </>
          )}
          <Col md={4} className="ra-negate-padding">
            <InputComponent
              label="Duration of Task"
              name="task_duration"
              value={`${form.task_duration}`}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Enter No. of Days/Hours Required"
              type="text"
              formControlId="task_duration"
              maxLength={255}
              form={form}
              helpText="Mention if the values are in Days/Hours"
              error={
                touched.task_duration ? validationErrors.task_duration : ''
              }
            />
          </Col>
          <Col md={9} className="ra-negate-padding">
            <div
              className="py-2"
              style={{
                gap: 10,
                borderRadius: '4px',
                paddingTop: 12,
                paddingRight: 16,
                paddingBottom: 12,
                paddingLeft: 16,
                fontSize: '14px',
                fontWeight: 400,
                color: '#1F4A70',
                lineHeight: '20px',
                background: '#F3F6F8',
              }}
            >
              <span>
                <strong>Note:</strong> If the defined job duration exceeds,
                office must be notified for any extension or alternate action.
              </span>
            </div>
          </Col>
          <hr />
          <Col md={9} className="ra-negate-padding">
            <InputComponent
              label="Alternative Considered to carry out above task"
              name="task_alternative_consideration"
              value={form.task_alternative_consideration}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="List the Alternatives Considered for the Task"
              type="textarea"
              formControlId="task_alternative_consideration"
              form={form}
              maxLength={4000}
              showMaxLength
              rows={3}
              error={
                touched.task_alternative_consideration
                  ? validationErrors.task_alternative_consideration
                  : ''
              }
            />
            <InputComponent
              label="Reason for Rejection"
              name="task_rejection_reason"
              value={form.task_rejection_reason}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="List the Reasons for Rejecting the Alternatives"
              type="textarea"
              formControlId="task_rejection_reason"
              form={form}
              showMaxLength
              maxLength={4000}
              rows={3}
              error={
                touched.task_rejection_reason
                  ? validationErrors.task_rejection_reason
                  : ''
              }
            />
          </Col>
        </Form>
      </Col>
    );
  },
);

BasicDetails.displayName = 'BasicDetails';

export {BasicDetails};
